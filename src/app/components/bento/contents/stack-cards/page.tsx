"use client";

import ThreeDStackDisplayCards from "@/components/bento/contents/3DStackDisplayCards";
import { <PERSON><PERSON><PERSON>, Star, Zap } from "lucide-react";

const customCards = [
  {
    Icon: Sparkles,
    title: "Featured",
    description: "Discover amazing content",
    date: "Just now",
  },
  {
    Icon: Star,
    title: "Popular",
    description: "Trending this week",
    date: "2 days ago",
  },
  {
    Icon: Zap,
    title: "New",
    description: "Latest updates and features",
    date: "Today",
  },
];

export default function page() {
  return (
    <div className="flex min-h-[400px] w-full items-center justify-center py-20">
      <div className="w-full max-w-3xl">
        <ThreeDStackDisplayCards cards={customCards} />
      </div>
    </div>
  );
}
