"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const buttons = [
  {
    name: "Globe",
    href: "/components/bento/contents/globe",
  },
  {
    name: "Stack Cards",
    href: "/components/bento/contents/stack-cards",
  },
];

export default function ButtonsPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {buttons.map((button) => (
            <Link key={button.name} href={button.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{button.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
