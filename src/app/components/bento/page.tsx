"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const bentoComponents: Array<{ name: string; href: string }> = [
  { name: "BentoBase", href: "/components/bento/BentoBase" },
  { name: "BentoDepthThreeD", href: "/components/bento/BentoDepthThreeD" },
  { name: "BentoMediaGallery", href: "/components/bento/BentoMediaGallery" },
  { name: "BentoContentReveal", href: "/components/bento/BentoContentReveal" },
  { name: "BentoBorderFill", href: "/components/bento/BentoBorderFill" },
  {
    name: "BentoHoverBorderGlow",
    href: "/components/bento/BentoHoverBorderGlow",
  },
  { name: "BentoHoverPattern", href: "/components/bento/BentoHoverPattern" },
  {
    name: "BentoHoverInfoReveal",
    href: "/components/bento/BentoHoverInfoReveal",
  },
  { name: "BentoKPlSimple", href: "/components/bento/BentoKPlSimple" },
  { name: "BentoStepsimple", href: "/components/bento/BentoStepsimple" },
  { name: "BentoKPlStandard", href: "/components/bento/BentoKPlStandard" },
  { name: "BentoPricingTable", href: "/components/bento/BentoPricingTable" },
  { name: "Bento Contents", href: "/components/bento/contents" },
];

export default function BentoPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {bentoComponents.map((component) => (
            <Link key={component.name} href={component.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{component.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
