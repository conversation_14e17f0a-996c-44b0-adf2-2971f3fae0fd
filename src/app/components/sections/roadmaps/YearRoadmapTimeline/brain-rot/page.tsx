"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import YearRoadmapTimeline from "@/components/sections/layouts/roadmap/YearRoadmapTimeline";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';
import { type TimelineYearlyItem } from "@/components/timeline/TimelineYearly";

function BrainRotYearRoadmapTimelineContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const roadmapData: TimelineYearlyItem[] = [
    {
      year: "2024",
      title: "THE GENESIS",
      description: "Token launch, community building, and initial viral marketing campaigns. Establishing the foundation for total market domination."
    },
    {
      year: "2025", 
      title: "VIRAL EXPLOSION",
      description: "Major exchange listings, influencer partnerships, and cross-platform meme warfare. Achieving mainstream recognition and adoption."
    },
    {
      year: "2026",
      title: "UTILITY REVOLUTION", 
      description: "Launch of staking mechanisms, NFT marketplace, and exclusive holder benefits. Real utility meets maximum degeneracy."
    },
    {
      year: "2027",
      title: "GALACTIC DOMINATION",
      description: "Cross-chain expansion, strategic partnerships, and global market penetration. The brain rot revolution goes interplanetary."
    }
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav position="bottom" />
        <SiteThemeProvider theme={{ styleVariant: 'brainRot', colorTemplate: colorTemplate, textAnimation: 'slide' }}>
          <YearRoadmapTimeline
            items={roadmapData} 
          />
        </SiteThemeProvider>
      </div>
    </ReactLenis>
  );
}

export default function BrainRotYearRoadmapTimelinePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <BrainRotYearRoadmapTimelineContent />
    </Suspense>
  );
}
