"use client";

import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import NavbarBase from "@/components/sections/layouts/navigation/NavbarBase";

function BrainRotNavbarContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <NavbarBase
          leftButtonText="Menu"
          rightButtonText="Contact"
          onLeftButtonClick={() => console.log("Menu clicked")}
          onRightButtonClick={() => console.log("Contact clicked")}
        />
        <div className="h-screen flex items-center justify-center bg-black">
          <h1 className="text-3xl text-white font-black uppercase">
            Brain Rot Simple Navbar - Theme {colorTemplate}
          </h1>
        </div>
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotPage() {
  return (
    <Suspense>
      <BrainRotNavbarContent />
    </Suspense>
  );
}
