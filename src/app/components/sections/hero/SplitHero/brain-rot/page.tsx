"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import SplitHero from "@/components/sections/layouts/hero/SplitHero";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function BrainRotSplitHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <SplitHero />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotSplitHeroPage() {
  return (
    <Suspense>
      <BrainRotSplitHeroContent />
    </Suspense>
  );
}
