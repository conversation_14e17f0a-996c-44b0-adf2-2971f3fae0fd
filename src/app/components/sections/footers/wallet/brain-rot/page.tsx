"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import WalletFooter from "@/components/sections/layouts/footer/WalletFooter";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function BrainRotWalletFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    logoText: "BRAINROT",
    walletAddress: "******************************************",
    copyButtonText: "COPY",
    copiedText: "COPIED!",
    copyrightText: "© 2025 | BRAIN ROT REVOLUTION",
    onSocialClick: (index: number) =>
      console.log(`Social icon ${index + 1} clicked`),
  };

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "brainRot",
        colorTemplate: theme,
        textAnimation: "slide",
      }}
    >
      <div className="min-h-screen bg-black flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-white p-8">
            <h1 className="text-4xl font-black uppercase mb-4">BRAIN ROT WALLET FOOTER</h1>
            <p className="text-xl font-bold uppercase">SCROLL DOWN TO SEE THE FOOTER</p>
          </div>
        </div>
        <WalletFooter
          logoText={footerData.logoText}
          walletAddress={footerData.walletAddress}
          copyButtonText={footerData.copyButtonText}
          copiedText={footerData.copiedText}
          copyrightText={footerData.copyrightText}
          onSocialClick={footerData.onSocialClick}
        />
      </div>
    </SiteThemeProvider>
  );
}

export default function BrainRotWalletFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <BrainRotWalletFooterContent />
      </Suspense>
    </ReactLenis>
  );
}
