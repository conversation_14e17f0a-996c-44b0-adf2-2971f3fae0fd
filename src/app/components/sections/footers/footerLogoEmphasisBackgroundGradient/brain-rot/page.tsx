"use client";

import React, { Suspense } from "react";
import { <PERSON><PERSON><PERSON>eni<PERSON> } from "lenis/react";
import FooterLogoEmphasisBackgroundGradient from "@/components/sections/layouts/footer/GradientFooter";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function BrainRotFooterLogoEmphasisBackgroundGradientContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    items: [
      { label: "ABOUT", onClick: () => console.log("About clicked") },
      { label: "BLOG", onClick: () => console.log("Blog clicked") },
      { label: "CAREERS", onClick: () => console.log("Careers clicked") },
      { label: "CONTACT", onClick: () => console.log("Contact clicked") },
      { label: "COMMUNITY", onClick: () => console.log("Community clicked") },
      { label: "SUPPORT", onClick: () => console.log("Support clicked") },
      { label: "TERMS", onClick: () => console.log("Terms clicked") },
      { label: "PRIVACY", onClick: () => console.log("Privacy clicked") },
    ],
  };

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "brainRot",
        colorTemplate: colorTemplate,
        textAnimation: "slide",
      }}
    >
      <div className="min-h-screen bg-black flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-white p-8">
            <h1 className="text-4xl font-black uppercase mb-4">BRAIN ROT GRADIENT FOOTER</h1>
            <p className="text-xl font-bold uppercase">SCROLL DOWN TO SEE THE FOOTER</p>
          </div>
        </div>
        <FooterLogoEmphasisBackgroundGradient {...footerData} />
      </div>
    </SiteThemeProvider>
  );
}

export default function BrainRotFooterLogoEmphasisBackgroundGradientPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <BrainRotFooterLogoEmphasisBackgroundGradientContent />
      </Suspense>
    </ReactLenis>
  );
}
