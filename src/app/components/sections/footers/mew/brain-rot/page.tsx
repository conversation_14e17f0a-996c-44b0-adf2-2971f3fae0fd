"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import MewFooter from "@/components/sections/layouts/footer/MewFooter";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function BrainRotMewFooterContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    title: "JOIN THE BRAIN ROT REVOLUTION… THE NORMIES WILL NEVER UNDERSTAND OUR POWER.",
    socialLinks: [
      { platform: "github", onClick: () => console.log("Github clicked") },
      { platform: "instagram", onClick: () => console.log("Instagram clicked") },
      { platform: "framer", onClick: () => console.log("Framer clicked") },
      { platform: "twitter", onClick: () => console.log("Twitter clicked") },
    ],
  };

  return (
    <SiteThemeProvider theme={{ styleVariant: 'brainRot', colorTemplate: colorTemplate, textAnimation: 'slide' }}>
      <div className="min-h-screen bg-black flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-white p-8">
            <h1 className="text-4xl font-black uppercase mb-4">BRAIN ROT MEW FOOTER</h1>
            <p className="text-xl font-bold uppercase">SCROLL DOWN TO SEE THE FOOTER</p>
          </div>
        </div>
        <MewFooter {...footerData} />
      </div>
    </SiteThemeProvider>
  );
}

export default function BrainRotMewFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <BrainRotMewFooterContent />
      </Suspense>
    </ReactLenis>
  );
}
