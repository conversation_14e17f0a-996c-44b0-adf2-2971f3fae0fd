"use client";

import React, { Suspense } from "react";
import { <PERSON>act<PERSON>enis } from "lenis/react";
import FooterLogoEmphasis from "@/components/sections/layouts/footer/LogoFooter";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function BrainRotFooterLogoEmphasisContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    columns: [
      {
        title: "COMPANY",
        items: [
          { label: "ABOUT", onClick: () => console.log("About clicked") },
          { label: "BLOG", onClick: () => console.log("Blog clicked") },
          { label: "CAREERS", onClick: () => console.log("Careers clicked") },
          { label: "CONTACT", onClick: () => console.log("Contact clicked") },
        ],
      },
      {
        title: "RESOURCES",
        items: [
          {
            label: "COMMUNITY",
            onClick: () => console.log("Community clicked"),
          },
          { label: "SUPPORT", onClick: () => console.log("Support clicked") },
          { label: "STATUS", onClick: () => console.log("Status clicked") },
          { label: "PARTNERS", onClick: () => console.log("Partners clicked") },
        ],
      },
      {
        title: "LEGAL",
        items: [
          { label: "TERMS", onClick: () => console.log("Terms clicked") },
          {
            label: "PRIVACY POLICY",
            onClick: () => console.log("Privacy Policy clicked"),
          },
          { label: "FAQ", onClick: () => console.log("FAQ clicked") },
          {
            label: "CONTACT US",
            onClick: () => console.log("Contact us clicked"),
          },
        ],
      },
    ],
  };

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "brainRot",
        colorTemplate: colorTemplate,
        textAnimation: "slide",
      }}
    >
      <div className="min-h-screen bg-black flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-white p-8">
            <h1 className="text-4xl font-black uppercase mb-4">BRAIN ROT LOGO FOOTER</h1>
            <p className="text-xl font-bold uppercase">SCROLL DOWN TO SEE THE FOOTER</p>
          </div>
        </div>
        <FooterLogoEmphasis {...footerData} />
      </div>
    </SiteThemeProvider>
  );
}

export default function BrainRotFooterLogoEmphasisPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <BrainRotFooterLogoEmphasisContent />
      </Suspense>
    </ReactLenis>
  );
}
