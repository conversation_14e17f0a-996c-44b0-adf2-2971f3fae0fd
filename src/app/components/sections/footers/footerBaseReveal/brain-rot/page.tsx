"use client";

import React, { Suspense } from "react";
import { <PERSON>act<PERSON>enis } from "lenis/react";
import FooterBaseReveal from "@/components/sections/layouts/footer/RevealFooter";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function BrainRotFooterBaseRevealContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    columns: [
      {
        title: "COMPANY",
        items: [
          { label: "ABOUT", onClick: () => console.log("About clicked") },
          { label: "BLOG", onClick: () => console.log("Blog clicked") },
          { label: "CAREERS", onClick: () => console.log("Careers clicked") },
          { label: "CONTACT", onClick: () => console.log("Contact clicked") },
        ],
      },
      {
        title: "RESOURCES",
        items: [
          {
            label: "COMMUNITY",
            onClick: () => console.log("Community clicked"),
          },
          { label: "SUPPORT", onClick: () => console.log("Support clicked") },
          { label: "STATUS", onClick: () => console.log("Status clicked") },
          { label: "PARTNERS", onClick: () => console.log("Partners clicked") },
        ],
      },
      {
        title: "LEGAL",
        items: [
          { label: "TERMS", onClick: () => console.log("Terms clicked") },
          { label: "PRIVACY", onClick: () => console.log("Privacy clicked") },
          { label: "COOKIES", onClick: () => console.log("Cookies clicked") },
          { label: "LICENSE", onClick: () => console.log("License clicked") },
        ],
      },
    ],
    copyrightText: "© 2025 | BRAIN ROT REVOLUTION",
    onPrivacyClick: () => console.log("Privacy Policy clicked"),
  };

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "brainRot",
        colorTemplate: colorTemplate,
        textAnimation: "slide",
      }}
    >
      <div className="min-h-[200vh] bg-black flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-white p-8">
            <h1 className="text-4xl font-black uppercase mb-4">BRAIN ROT REVEAL FOOTER</h1>
            <p className="text-xl font-bold uppercase">SCROLL DOWN TO REVEAL THE FOOTER</p>
            <p className="text-lg font-bold uppercase mt-4">KEEP SCROLLING...</p>
          </div>
        </div>
        <FooterBaseReveal {...footerData} />
      </div>
    </SiteThemeProvider>
  );
}

export default function BrainRotFooterBaseRevealPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <BrainRotFooterBaseRevealContent />
      </Suspense>
    </ReactLenis>
  );
}
