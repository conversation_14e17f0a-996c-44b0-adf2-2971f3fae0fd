import AnimatedGradientBackground from "@/components/background/AnimatedGradientBackground";

const page = () => {
  return (
    <div className="relative w-full h-screen overflow-hidden">
      <AnimatedGradientBackground />
      <div className="relative z-10 flex flex-col items-center justify-start h-full px-4 pt-32 text-center">
        <p className="mt-4 text-lg bg-gradient-to-b from-gray-400 to-gray-600/70 bg-clip-text text-transparent md:text-7xl max-w-lg">
          WEBILD
        </p>
      </div>
    </div>
  );
};

export default page;
